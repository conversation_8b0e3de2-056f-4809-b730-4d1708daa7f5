const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.ChU8GYQd.js","../chunks/Bc-255wv.js","../chunks/xwHhHbJ3.js","../chunks/CARsfY4Y.js","../chunks/YCrX7EkR.js","../chunks/CbGKKmWg.js","../chunks/DblLzjgS.js","../assets/0.DGwCET-8.css","../nodes/1.Dp6XYEu1.js","../chunks/DPKhDFdk.js","../chunks/CJ0gORsW.js","../chunks/DteoZW-m.js","../chunks/Bj5Jjw6W.js","../chunks/CQGjpemh.js","../chunks/DucKLUER.js","../nodes/2.y3IRw2mO.js","../nodes/3.C6D37D_m.js","../chunks/C6NPqmlb.js","../chunks/DbSa5uNy.js","../chunks/Deiq1e4r.js","../chunks/D6jtUsr0.js","../nodes/4.7kUKRlbh.js","../nodes/5.wipIXV5g.js","../nodes/6.ChqZxzY3.js"])))=>i.map(i=>d[i]);
var W=r=>{throw TypeError(r)};var z=(r,t,s)=>t.has(r)||W("Cannot "+s);var u=(r,t,s)=>(z(r,t,"read from private field"),s?s.call(r):t.get(r)),S=(r,t,s)=>t.has(r)?W("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,s),C=(r,t,s,n)=>(z(r,t,"write to private field"),n?n.call(r,s):t.set(r,s),s);import{C as G,O as Q,y as X,E as Z,z as M,W as $,D as tt,b as x,a7 as et,g as v,aE as rt,ay as st,I as at,p as nt,m as ot,e as it,d as p,aF as ct,f as L,s as ut,a as lt,c as dt,r as ft,u as D,t as mt}from"../chunks/xwHhHbJ3.js";import{h as _t,m as ht,u as vt,s as gt}from"../chunks/CJ0gORsW.js";import{t as Y,a as O,c as I,b as yt}from"../chunks/Bc-255wv.js";import{o as Et}from"../chunks/DucKLUER.js";import{p as V,i as j,b as B}from"../chunks/C6NPqmlb.js";function F(r,t,s){G&&Q();var n=r,o,c;X(()=>{o!==(o=t())&&(c&&($(c),c=null),o&&(c=M(()=>s(n,o))))},Z),G&&(n=tt)}function bt(r){return class extends Pt{constructor(t){super({component:r,...t})}}}var g,d;class Pt{constructor(t){S(this,g);S(this,d);var c;var s=new Map,n=(a,e)=>{var f=at(e);return s.set(a,f),f};const o=new Proxy({...t.props||{},$$events:{}},{get(a,e){return v(s.get(e)??n(e,Reflect.get(a,e)))},has(a,e){return e===et?!0:(v(s.get(e)??n(e,Reflect.get(a,e))),Reflect.has(a,e))},set(a,e,f){return x(s.get(e)??n(e,f),f),Reflect.set(a,e,f)}});C(this,d,(t.hydrate?_t:ht)(t.component,{target:t.target,anchor:t.anchor,props:o,context:t.context,intro:t.intro??!1,recover:t.recover})),(!((c=t==null?void 0:t.props)!=null&&c.$$host)||t.sync===!1)&&rt(),C(this,g,o.$$events);for(const a of Object.keys(u(this,d)))a==="$set"||a==="$destroy"||a==="$on"||st(this,a,{get(){return u(this,d)[a]},set(e){u(this,d)[a]=e},enumerable:!0});u(this,d).$set=a=>{Object.assign(o,a)},u(this,d).$destroy=()=>{vt(u(this,d))}}$set(t){u(this,d).$set(t)}$on(t,s){u(this,g)[t]=u(this,g)[t]||[];const n=(...o)=>s.call(this,...o);return u(this,g)[t].push(n),()=>{u(this,g)[t]=u(this,g)[t].filter(o=>o!==n)}}$destroy(){u(this,d).$destroy()}}g=new WeakMap,d=new WeakMap;const Rt="modulepreload",Ot=function(r,t){return new URL(r,t).href},N={},P=function(t,s,n){let o=Promise.resolve();if(s&&s.length>0){const a=document.getElementsByTagName("link"),e=document.querySelector("meta[property=csp-nonce]"),f=(e==null?void 0:e.nonce)||(e==null?void 0:e.getAttribute("nonce"));o=Promise.allSettled(s.map(l=>{if(l=Ot(l,n),l in N)return;N[l]=!0;const y=l.endsWith(".css"),T=y?'[rel="stylesheet"]':"";if(!!n)for(let E=a.length-1;E>=0;E--){const i=a[E];if(i.href===l&&(!y||i.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${T}`))return;const _=document.createElement("link");if(_.rel=y?"stylesheet":Rt,y||(_.as="script"),_.crossOrigin="",_.href=l,f&&_.setAttribute("nonce",f),document.head.appendChild(_),y)return new Promise((E,i)=>{_.addEventListener("load",E),_.addEventListener("error",()=>i(new Error(`Unable to preload CSS for ${l}`)))})}))}function c(a){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=a,window.dispatchEvent(e),!e.defaultPrevented)throw a}return o.then(a=>{for(const e of a||[])e.status==="rejected"&&c(e.reason);return t().catch(c)})},jt={};var wt=Y('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),kt=Y("<!> <!>",1);function At(r,t){nt(t,!0);let s=V(t,"components",23,()=>[]),n=V(t,"data_0",3,null),o=V(t,"data_1",3,null);ot(()=>t.stores.page.set(t.page)),it(()=>{t.stores,t.page,t.constructors,s(),t.form,n(),o(),t.stores.page.notify()});let c=p(!1),a=p(!1),e=p(null);Et(()=>{const i=t.stores.page.subscribe(()=>{v(c)&&(x(a,!0),ct().then(()=>{x(e,document.title||"untitled page",!0)}))});return x(c,!0),i});const f=D(()=>t.constructors[1]);var l=kt(),y=L(l);{var T=i=>{var h=I();const w=D(()=>t.constructors[0]);var k=L(h);F(k,()=>v(w),(b,R)=>{B(R(b,{get data(){return n()},get form(){return t.form},children:(m,Tt)=>{var U=I(),H=L(U);F(H,()=>v(f),(J,K)=>{B(K(J,{get data(){return o()},get form(){return t.form}}),A=>s()[1]=A,()=>{var A;return(A=s())==null?void 0:A[1]})}),O(m,U)},$$slots:{default:!0}}),m=>s()[0]=m,()=>{var m;return(m=s())==null?void 0:m[0]})}),O(i,h)},q=i=>{var h=I();const w=D(()=>t.constructors[0]);var k=L(h);F(k,()=>v(w),(b,R)=>{B(R(b,{get data(){return n()},get form(){return t.form}}),m=>s()[0]=m,()=>{var m;return(m=s())==null?void 0:m[0]})}),O(i,h)};j(y,i=>{t.constructors[1]?i(T):i(q,!1)})}var _=ut(y,2);{var E=i=>{var h=wt(),w=dt(h);{var k=b=>{var R=yt();mt(()=>gt(R,v(e))),O(b,R)};j(w,b=>{v(a)&&b(k)})}ft(h),O(i,h)};j(_,i=>{v(c)&&i(E)})}O(r,l),lt()}const Bt=bt(At),Ft=[()=>P(()=>import("../nodes/0.ChU8GYQd.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url),()=>P(()=>import("../nodes/1.Dp6XYEu1.js"),__vite__mapDeps([8,1,2,9,10,11,12,13,14]),import.meta.url),()=>P(()=>import("../nodes/2.y3IRw2mO.js"),__vite__mapDeps([15,1,2,9]),import.meta.url),()=>P(()=>import("../nodes/3.C6D37D_m.js"),__vite__mapDeps([16,1,2,10,17,18,19,3,20]),import.meta.url),()=>P(()=>import("../nodes/4.7kUKRlbh.js"),__vite__mapDeps([21,1,2,10,17,18,19,3,12,13,14,20]),import.meta.url),()=>P(()=>import("../nodes/5.wipIXV5g.js"),__vite__mapDeps([22,1,2,9,10,17,11,19,4,5]),import.meta.url),()=>P(()=>import("../nodes/6.ChqZxzY3.js"),__vite__mapDeps([23,1,2,10,17,18,19,3,6,5]),import.meta.url)],qt=[],Ut={"/":[2],"/chat":[3],"/chat/[id]":[4],"/completion":[5],"/structured-object":[6]},Lt={handleError:({error:r})=>{console.error(r)},reroute:()=>{},transport:{}},xt=Object.fromEntries(Object.entries(Lt.transport).map(([r,t])=>[r,t.decode])),Wt=!1,zt=(r,t)=>xt[r](t);export{zt as decode,xt as decoders,Ut as dictionary,Wt as hash,Lt as hooks,jt as matchers,Ft as nodes,Bt as root,qt as server_loads};
