import{t as h,a as l}from"../chunks/Bc-255wv.js";import"../chunks/DPKhDFdk.js";import{p as v,f as u,t as g,a as x,c as e,r as p,s as _}from"../chunks/xwHhHbJ3.js";import{s as o}from"../chunks/CJ0gORsW.js";import{i as d}from"../chunks/DteoZW-m.js";import{p as m}from"../chunks/Bj5Jjw6W.js";var b=h("<h1> </h1> <p> </p>",1);function z(i,f){v(f,!1),d();var r=b(),t=u(r),n=e(t,!0);p(t);var a=_(t,2),c=e(a,!0);p(a),g(()=>{var s;o(n,m.status),o(c,(s=m.error)==null?void 0:s.message)}),l(i,r),x()}export{z as component};
