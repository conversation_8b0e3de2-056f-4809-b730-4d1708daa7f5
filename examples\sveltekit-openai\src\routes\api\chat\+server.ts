import { env } from '$env/dynamic/private';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { streamText, stepCountIs, smoothStream,convertToModelMessages } from 'ai';
import { z } from 'zod/v4';
const openai = createOpenAICompatible({
  name: "KimiK2",
  apiKey: "sk-kro0rwMTQQOVnik13gL0gtUtO2M0Xs46qTovInLa4qZAUiLM",
  baseURL: "https://api.moonshot.cn/v1",
  
});
export function logStream(originalStream: ReadableStream) {
  const [loggedStream, loggingStream] = originalStream.tee();
  return new ReadableStream({
    async start(controller) {
      const reader = loggingStream.getReader();
      async function read() {
        const { done, value } = await reader.read();
        if (done) {
          controller.close();
          return;
        }
        controller.enqueue(value);
        await new Promise(resolve => setTimeout(resolve, 80));
        read();
      }
      read();
    }
  });
}

export const POST = async ({ request }: { request: Request }) => {
  const { messages } = await request.json();

  const result = streamText({
    model: openai('kimi-k2-0711-preview'),
    messages: convertToModelMessages(messages),
    
    experimental_transform: smoothStream(),
    stopWhen: stepCountIs(5), // multi-steps for server-side tools
    tools: {
      // server-side tool with execute function:
      getWeatherInformation: {
        description: 'show the weather in a given city to the user',
        inputSchema: z.object({ city: z.string() }),
        execute: async ({ city: _ }: { city: string }) => {
          // Add artificial delay of 2 seconds
          await new Promise(resolve => setTimeout(resolve, 2000));

          const weatherOptions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
          return weatherOptions[
            Math.floor(Math.random() * weatherOptions.length)
          ];
        },
      },
      // client-side tool that starts user interaction:
      askForConfirmation: {
        description: 'Ask the user for confirmation.',
        inputSchema: z.object({
          message: z.string().describe('The message to ask for confirmation.'),
        }),
      },
      // client-side tool that is automatically executed on the client:
      getLocation: {
        description:
          'Get the user location. Always ask for confirmation before using this tool.',
        inputSchema: z.object({}),
      },
    },
    on
    onError: error => {
      console.error(error);
    },
  });
  
  return result.toUIMessageStreamResponse();
};
