import { env } from '$env/dynamic/private';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { streamText, stepCountIs, smoothStream, convertToModelMessages } from 'ai';
import { z } from 'zod/v4';
const openai = createOpenAICompatible({
  name: "KimiK2",
  apiKey: "sk-kro0rwMTQQOVnik13gL0gtUtO2M0Xs46qTovInLa4qZAUiLM",
  baseURL: "https://api.moonshot.cn/v1",
  
});
// 创建一个实时流，移除缓冲延迟，支持更好的错误处理
function logStream(originalStream: ReadableStream) {
  return new ReadableStream({
    async start(controller) {
      const reader = originalStream.getReader();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            controller.close();
            break;
          }

          // 立即传递数据，无延迟
          controller.enqueue(value);

          // 可选：添加微小的 yield 来避免阻塞事件循环
          // await new Promise(resolve => setTimeout(resolve, 0));
        }
      } catch (error) {
        console.error('Stream error:', error);
        controller.error(error);
      } finally {
        reader.releaseLock();
      }
    },

    // 支持流的取消
    cancel() {
      console.log('Stream cancelled');
    }
  });
}

// 创建自定义的流响应，类似于 StreamingTextResponse
function createStreamingResponse(stream: ReadableStream, headers?: HeadersInit) {
  return new Response(stream, {
    status: 200,
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'X-Accel-Buffering': 'no', // 禁用 nginx 缓冲
      ...headers,
    },
  });
}

export const POST = async ({ request }: { request: Request }) => {
  const { messages } = await request.json();

  const result = streamText({
    model: openai('kimi-k2-0711-preview'),
    messages: convertToModelMessages(messages),
    
    // experimental_transform: smoothStream(), // 移除 smoothStream 以获得更实时的输出
    stopWhen: stepCountIs(5), // multi-steps for server-side tools
    tools: {
      // server-side tool with execute function:
      getWeatherInformation: {
        description: 'show the weather in a given city to the user',
        inputSchema: z.object({ city: z.string() }),
        execute: async ({ city: _ }: { city: string }) => {
          // Add artificial delay of 2 seconds
          await new Promise(resolve => setTimeout(resolve, 2000));

          const weatherOptions = ['sunny', 'cloudy', 'rainy', 'snowy', 'windy'];
          return weatherOptions[
            Math.floor(Math.random() * weatherOptions.length)
          ];
        },
      },
      // client-side tool that starts user interaction:
      askForConfirmation: {
        description: 'Ask the user for confirmation.',
        inputSchema: z.object({
          message: z.string().describe('The message to ask for confirmation.'),
        }),
      },
      // client-side tool that is automatically executed on the client:
      getLocation: {
        description:
          'Get the user location. Always ask for confirmation before using this tool.',
        inputSchema: z.object({}),
      },
    },
    onChunk: (chunk) => {
      console.log(chunk);
    },
    onError: error => {
      console.error(error);
    },
  });
  
  // 选择你想要的流输出方案：

  // 方案一：使用自定义的实时流（移除所有缓冲延迟）
  const stream = result.toUIMessageStream();
  const realTimeStream = logStream(stream);
  return createStreamingResponse(realTimeStream);

  // 方案二：直接使用 textStream 获得更实时的纯文本流（最快）
  // return new Response(result.textStream.pipeThrough(new TextEncoderStream()), {
  //   headers: {
  //     'Content-Type': 'text/plain; charset=utf-8',
  //     'Cache-Control': 'no-cache',
  //     'X-Accel-Buffering': 'no',
  //   },
  // });

  // 方案三：使用默认的 toUIMessageStreamResponse（有缓冲）
  // return result.toUIMessageStreamResponse();
};
