

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.Dp6XYEu1.js","_app/immutable/chunks/Bc-255wv.js","_app/immutable/chunks/xwHhHbJ3.js","_app/immutable/chunks/DPKhDFdk.js","_app/immutable/chunks/CJ0gORsW.js","_app/immutable/chunks/DteoZW-m.js","_app/immutable/chunks/Bj5Jjw6W.js","_app/immutable/chunks/CQGjpemh.js","_app/immutable/chunks/DucKLUER.js"];
export const stylesheets = [];
export const fonts = [];
